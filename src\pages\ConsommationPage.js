import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ConsommationPage = ({ user, onBack }) => {
  const [consommations, setConsommations] = useState([]);
  const [clients, setClients] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [newConsommation, setNewConsommation] = useState({
    idcont: '',
    consommationpre: '',
    consommationactuelle: '',
    jours: 30,
    periode: new Date().toISOString().slice(0, 7) // YYYY-MM format
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Configuration de l'API
  const API_BASE_URL = 'http://localhost:3003';

  useEffect(() => {
    // Charger les données depuis la base de données
    fetchClients();
    fetchContracts();
    fetchConsommations();
  }, []);

  const fetchClients = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/table/client`);
      const data = await response.json();
      if (data.success) {
        setClients(data.data || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
      setError('Erreur lors du chargement des clients');
    }
  };

  const fetchContracts = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/table/contract`);
      const data = await response.json();
      if (data.success) {
        setContracts(data.data || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des contrats:', error);
    }
  };

  const fetchConsommations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/table/consommation`);
      const data = await response.json();
      if (data.success) {
        setConsommations(data.data || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des consommations:', error);
      setError('Erreur lors du chargement des consommations');
    } finally {
      setLoading(false);
    }
  };

  const handleAddConsommation = async (e) => {
    e.preventDefault();

    if (!newConsommation.idcont || !newConsommation.consommationactuelle) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/consommations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newConsommation,
          idtech: user?.idtech || 1, // ID du technicien connecté
          idtranch: 1 // Tranche par défaut
        })
      });

      const data = await response.json();

      if (data.success) {
        alert('Consommation ajoutée avec succès !');
        setNewConsommation({
          idcont: '',
          consommationpre: '',
          consommationactuelle: '',
          jours: 30,
          periode: new Date().toISOString().slice(0, 7)
        });
        // Recharger les consommations
        fetchConsommations();
      } else {
        alert('Erreur lors de l\'ajout: ' + data.error);
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la consommation:', error);
      alert('Erreur lors de l\'ajout de la consommation');
    }
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Consommation d'Eau</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Consommation d'Eau</h1>
            <p className="tech-mobile-card-subtitle">Saisie et consultation des relevés</p>
          </div>
        </div>
      </div>

      {/* Formulaire de saisie */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Nouveau Relevé</h2>
        </div>
        <form onSubmit={handleAddConsommation} className="tech-mobile-form">
          <div className="tech-mobile-form-group">
            <label>Contrat</label>
            <select
              value={newConsommation.idcont}
              onChange={(e) => setNewConsommation({...newConsommation, idcont: e.target.value})}
              className="tech-mobile-form-input"
              required
            >
              <option value="">Sélectionner un contrat</option>
              {contracts.map(contract => {
                const client = clients.find(c => c.idclient === contract.idclient);
                return (
                  <option key={contract.idcontract} value={contract.idcontract}>
                    {contract.codeqr} - {client?.nom} {client?.prenom}
                  </option>
                );
              })}
            </select>
          </div>

          <div className="tech-mobile-form-group">
            <label>Consommation Précédente (m³)</label>
            <input
              type="number"
              step="0.1"
              placeholder="Ex: 100.0"
              value={newConsommation.consommationpre}
              onChange={(e) => setNewConsommation({...newConsommation, consommationpre: e.target.value})}
              className="tech-mobile-form-input"
            />
          </div>

          <div className="tech-mobile-form-group">
            <label>Consommation Actuelle (m³) *</label>
            <input
              type="number"
              step="0.1"
              placeholder="Ex: 125.5"
              value={newConsommation.consommationactuelle}
              onChange={(e) => setNewConsommation({...newConsommation, consommationactuelle: e.target.value})}
              className="tech-mobile-form-input"
              required
            />
          </div>

          <div className="tech-mobile-form-group">
            <label>Nombre de jours</label>
            <input
              type="number"
              placeholder="30"
              value={newConsommation.jours}
              onChange={(e) => setNewConsommation({...newConsommation, jours: e.target.value})}
              className="tech-mobile-form-input"
            />
          </div>

          <div className="tech-mobile-form-group">
            <label>Période (YYYY-MM)</label>
            <input
              type="month"
              value={newConsommation.periode}
              onChange={(e) => setNewConsommation({...newConsommation, periode: e.target.value})}
              className="tech-mobile-form-input"
            />
          </div>

          <button
            type="submit"
            className="tech-mobile-action-btn complete"
            style={{ width: '100%', marginTop: '10px' }}
          >
            Enregistrer le relevé
          </button>
        </form>
      </div>

      {/* Liste des consommations récentes */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Relevés Récents</h2>
          <p className="tech-mobile-card-subtitle">{consommations.length} relevé(s)</p>
        </div>
      </div>

      {consommations.map(consommation => (
        <div key={consommation.id} className="tech-mobile-intervention-item">
          <div className="tech-mobile-intervention-header">
            <div className="tech-mobile-intervention-client">
              <strong>{consommation.clientNom}</strong>
            </div>
            <div className="tech-mobile-intervention-badge">
              {consommation.valeur} m³
            </div>
          </div>

          <div className="tech-mobile-intervention-details">
            <div className="tech-mobile-intervention-info">
              <span>📅 {new Date(consommation.date).toLocaleDateString('fr-FR')}</span>
            </div>
            <div className="tech-mobile-intervention-info">
              <span>🔢 Compteur: {consommation.compteur}</span>
            </div>
          </div>
        </div>
      ))}

      {consommations.length === 0 && (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">💧</div>
            <h3>Aucun relevé enregistré</h3>
            <p>Commencez par ajouter un nouveau relevé de consommation.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConsommationPage;
