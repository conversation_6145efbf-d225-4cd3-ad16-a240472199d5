require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function testConnection() {
  console.log('🔄 Test de connexion à la base de données...');
  console.log('📋 Configuration :');
  console.log(`   - Utilisateur: ${process.env.DB_USER || 'postgres'}`);
  console.log(`   - Hôte: ${process.env.DB_HOST || 'localhost'}`);
  console.log(`   - Base: ${process.env.DB_NAME || 'Facuration'}`);
  console.log(`   - Port: ${process.env.DB_PORT || 5432}`);
  console.log('');

  try {
    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion à PostgreSQL réussie !');
    
    // Lister toutes les bases de données disponibles
    console.log('\n📊 Bases de données disponibles :');
    const databases = await client.query('SELECT datname FROM pg_database WHERE datistemplate = false;');
    databases.rows.forEach(row => {
      const marker = row.datname === (process.env.DB_NAME || 'Facturation') ? '👉' : '  ';
      console.log(`${marker} ${row.datname}`);
    });

    // Lister les tables de la base actuelle
    console.log(`\n📋 Tables dans la base "${process.env.DB_NAME || 'Facturation'}" :`);
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    if (tables.rows.length === 0) {
      console.log('❌ Aucune table trouvée dans cette base de données');
      console.log('💡 Vérifiez que vous utilisez la bonne base de données');
    } else {
      tables.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
    }

    // Compter les enregistrements dans chaque table
    if (tables.rows.length > 0) {
      console.log('\n📊 Nombre d\'enregistrements par table :');
      for (const table of tables.rows) {
        try {
          const count = await client.query(`SELECT COUNT(*) FROM ${table.table_name}`);
          console.log(`   - ${table.table_name}: ${count.rows[0].count} enregistrement(s)`);
        } catch (error) {
          console.log(`   - ${table.table_name}: Erreur lors du comptage`);
        }
      }
    }

    client.release();
    console.log('\n🎉 Test de connexion terminé avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données :');
    console.error(`   ${error.message}`);
    console.log('\n🔧 Solutions possibles :');
    console.log('   1. Vérifiez que PostgreSQL est démarré');
    console.log('   2. Vérifiez le nom d\'utilisateur et mot de passe');
    console.log('   3. Vérifiez que la base de données existe');
    console.log('   4. Vérifiez le port (par défaut 5432)');
    console.log('   5. Modifiez le fichier .env avec vos paramètres');
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testConnection();
