require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function verifyFacutrationData() {
  console.log('🔍 Vérification de la base de données "Facutration"...');
  console.log(`📋 Connexion à: ${process.env.DB_NAME || 'Facutration'}`);
  console.log('');

  try {
    const client = await pool.connect();
    
    // Vérifier le nom de la base actuelle
    const currentDB = await client.query('SELECT current_database()');
    console.log(`✅ Base de données actuelle: "${currentDB.rows[0].current_database}"`);
    console.log('');

    // Lister toutes les tables et leurs données
    const tables = ['client', 'consommation', 'contract', 'secteur', 'tranch', 'utilisateur'];
    
    for (const tableName of tables) {
      console.log(`📋 Table: ${tableName}`);
      
      try {
        // Compter les enregistrements
        const count = await client.query(`SELECT COUNT(*) FROM ${tableName}`);
        console.log(`   📊 Nombre d'enregistrements: ${count.rows[0].count}`);
        
        // Afficher quelques exemples de données
        if (parseInt(count.rows[0].count) > 0) {
          const sample = await client.query(`SELECT * FROM ${tableName} LIMIT 3`);
          
          if (sample.rows.length > 0) {
            console.log(`   📄 Exemples de données:`);
            sample.rows.forEach((row, index) => {
              console.log(`      ${index + 1}. ${JSON.stringify(row)}`);
            });
          }
        } else {
          console.log(`   ⚠️  Table vide`);
        }
        
        console.log('');
      } catch (error) {
        console.log(`   ❌ Erreur lors de la lecture: ${error.message}`);
        console.log('');
      }
    }

    // Vérifier spécifiquement les utilisateurs pour la connexion
    console.log('🔐 Vérification des utilisateurs pour la connexion:');
    try {
      const users = await client.query(`
        SELECT idtech, nom, prenom, email, role 
        FROM utilisateur 
        WHERE email LIKE '%@%'
      `);
      
      if (users.rows.length > 0) {
        users.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.email} (${user.role}) - ${user.prenom} ${user.nom}`);
        });
      } else {
        console.log('   ⚠️  Aucun utilisateur avec email trouvé');
      }
    } catch (error) {
      console.log(`   ❌ Erreur: ${error.message}`);
    }

    console.log('');
    console.log('🎯 Résumé de la base "Facutration":');
    
    // Résumé final
    for (const tableName of tables) {
      try {
        const count = await client.query(`SELECT COUNT(*) FROM ${tableName}`);
        const emoji = parseInt(count.rows[0].count) > 0 ? '✅' : '⚠️';
        console.log(`   ${emoji} ${tableName}: ${count.rows[0].count} enregistrement(s)`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: Erreur de lecture`);
      }
    }

    client.release();
    console.log('');
    console.log('🎉 Vérification terminée !');
    console.log('💡 Votre dashboard va maintenant utiliser les données de "Facutration"');
    
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
  } finally {
    await pool.end();
  }
}

// Exécuter la vérification
verifyFacutrationData();
