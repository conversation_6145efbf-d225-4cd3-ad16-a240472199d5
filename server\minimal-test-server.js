const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur de test fonctionnel', 
    timestamp: new Date().toISOString() 
  });
});

// Route de connexion de test (sans base de données)
app.post('/login', async (req, res) => {
  console.log('📥 Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    console.log('🔍 Test de connexion pour:', email);
    
    // Test avec les identifiants du technicien
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion réussie pour le technicien');
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech'
        }
      });
    }
    
    // Test avec les identifiants admin
    if (email === '<EMAIL>' && motDepass === 'admin123') {
      console.log('✅ Connexion réussie pour l\'admin');
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 2,
          nom: 'Admin',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Admin'
        }
      });
    }

    // Identifiants incorrects
    console.log('❌ Identifiants incorrects pour:', email);
    return res.status(401).json({ 
      success: false,
      message: 'Email ou mot de passe incorrect' 
    });

  } catch (error) {
    console.error('❌ Erreur de connexion:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erreur serveur',
      error: error.message 
    });
  }
});

// Démarrer le serveur
const PORT = process.env.BACKEND_PORT || 3002;
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test démarré sur http://localhost:${PORT}`);
  console.log('🔑 Comptes de test disponibles:');
  console.log('  - <EMAIL> / Tech123 (Technicien)');
  console.log('  - <EMAIL> / admin123 (Admin)');
  console.log('📡 Serveur prêt à recevoir des requêtes');
});
