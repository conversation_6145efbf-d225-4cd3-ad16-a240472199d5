require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test simple
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur test clients fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/test', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) as count FROM client');
    client.release();
    
    res.json({
      success: true,
      message: 'Test DB réussi',
      clientCount: result.rows[0].count
    });
  } catch (err) {
    console.error('Erreur DB:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📋 Récupération des clients...');
    const client = await pool.connect();
    
    const query = `
      SELECT 
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;
    
    const result = await client.query(query);
    client.release();
    
    console.log(`✅ ${result.rows.length} clients récupérés`);
    
    res.json({
      success: true,
      message: 'Clients récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur clients:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur test clients démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/test (test DB)');
  console.log('  - GET /api/clients (liste clients)');
});
